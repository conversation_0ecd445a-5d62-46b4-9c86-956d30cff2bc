<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客查询页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #409eff;
        }
        .link-section {
            margin: 20px 0;
        }
        .test-link {
            display: inline-block;
            background: #409eff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 10px 10px 0;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background: #337ecc;
        }
        .features {
            margin-top: 30px;
        }
        .feature-item {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .feature-item strong {
            color: #409eff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 游客查询页面测试</h1>
        
        <div class="info">
            <strong>📋 功能说明：</strong><br>
            游客查询页面已成功创建，支持对数据信息进行精确结果匹配查询，不支持类型和用户批次筛选。
        </div>

        <div class="link-section">
            <h3>🚀 测试链接：</h3>
            <a href="http://localhost:80/guest-query" class="test-link" target="_blank">
                访问游客查询页面
            </a>
            <a href="http://localhost:80/guest-query?value=测试数据" class="test-link" target="_blank">
                带参数访问（测试数据）
            </a>
        </div>

        <div class="features">
            <h3>✨ 页面特性：</h3>
            <div class="feature-item">
                <strong>精确匹配：</strong> 只支持对"结果"字段进行精确匹配查询
            </div>
            <div class="feature-item">
                <strong>简洁界面：</strong> 专为游客设计的简洁查询界面
            </div>
            <div class="feature-item">
                <strong>实时搜索：</strong> 支持回车键快速搜索
            </div>
            <div class="feature-item">
                <strong>状态提示：</strong> 提供搜索前、搜索中、无结果等状态提示
            </div>
            <div class="feature-item">
                <strong>URL参数：</strong> 支持通过URL参数预填充查询条件
            </div>
        </div>

        <div class="info" style="margin-top: 30px; background: #fff2e8; border-left-color: #e6a23c;">
            <strong>⚠️ 注意事项：</strong><br>
            1. 确保后端API接口 <code>/system/data-info/guest/query</code> 已实现<br>
            2. 页面路由为 <code>/guest-query</code><br>
            3. 查询功能需要输入完整的结果值进行精确匹配
        </div>
    </div>
</body>
</html>
