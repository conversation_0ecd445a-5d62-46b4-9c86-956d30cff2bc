<template>
  <div class="query-page">
    <!-- 搜索工作栏 -->
    <ContentWrap class="search-container">
      <div class="search-header">
        <h3 class="search-title">
          <Icon icon="ep:search" class="title-icon" />
          数据查询
        </h3>
        <p class="search-subtitle">请输入查询条件进行精确搜索</p>
      </div>

      <el-form
        class="search-form"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="80px"
      >
        <div class="form-row">
          <el-form-item label="查询结果" prop="value" class="form-item-custom">
            <el-input
              v-model="queryParams.value"
              placeholder="请输入精确结果进行查询"
              clearable
              @keyup.enter="handleQuery"
              class="search-input"
              prefix-icon="Search"
            />
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button type="primary" @click="handleQuery" class="search-btn">
            <Icon icon="ep:search" class="btn-icon" />
            搜索查询
          </el-button>
          <el-button @click="resetQuery" class="reset-btn">
            <Icon icon="ep:refresh" class="btn-icon" />
            重置条件
          </el-button>
        </div>
      </el-form>
    </ContentWrap>

    <!-- 结果展示区域 -->
    <ContentWrap class="result-container">
      <!-- 结果统计 -->
      <div v-if="hasSearched" class="result-stats">
        <div class="stats-item">
          <Icon icon="ep:document" class="stats-icon" />
          <span class="stats-text">共找到 <strong>{{ total }}</strong> 条结果</span>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        row-key="id"
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        class="data-table"
        element-loading-text="正在查询数据..."
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table-column label="编号" align="center" prop="id" width="80">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="数据类型" align="center" prop="type" width="120">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_DATA_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>

        <el-table-column label="查询结果" align="left" prop="value" min-width="200">
          <template #default="scope">
            <div class="result-cell">
              <Icon icon="ep:document-copy" class="result-icon" />
              <span class="result-text">{{ scope.row.value }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="批次编号" align="center" prop="batchId" width="120">
          <template #default="scope">
            <el-tag type="warning" size="small">{{ scope.row.batchId }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="用户编号" align="center" prop="userId" width="120">
          <template #default="scope">
            <el-tag type="success" size="small">{{ scope.row.userId }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          width="180"
        >
          <template #default="scope">
            <div class="time-cell">
              <Icon icon="ep:clock" class="time-icon" />
              <span>{{ dateFormatter(scope.row, null, scope.row.createTime) }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="total > 0">
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>

      <!-- 空状态展示 -->
      <div v-if="!loading && list.length === 0 && hasSearched" class="empty-state">
        <el-empty
          description="未找到匹配的查询结果"
          :image-size="120"
        >
          <template #description>
            <p class="empty-text">未找到匹配的查询结果</p>
            <p class="empty-hint">请尝试调整查询条件后重新搜索</p>
          </template>
          <el-button type="primary" @click="resetQuery">
            重新查询
          </el-button>
        </el-empty>
      </div>

      <!-- 初始状态 -->
      <div v-if="!loading && !hasSearched" class="welcome-state">
        <el-empty
          description="开始您的数据查询之旅"
          :image-size="150"
        >
          <template #description>
            <div class="welcome-content">
              <h4 class="welcome-title">欢迎使用数据查询系统</h4>
              <p class="welcome-text">请在上方输入查询条件，开始您的数据探索之旅</p>
              <div class="welcome-tips">
                <div class="tip-item">
                  <Icon icon="ep:info-filled" class="tip-icon" />
                  <span>支持精确结果匹配查询</span>
                </div>
                <div class="tip-item">
                  <Icon icon="ep:calendar" class="tip-icon" />
                  <span>可按时间范围筛选数据</span>
                </div>
              </div>
            </div>
          </template>
        </el-empty>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { DataInfoApi, DataInfo } from '@/api/system/datainfo'

/** 游客数据查询 列表 */
defineOptions({ name: 'GuestDataQuery' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<DataInfo[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const hasSearched = ref(false) // 是否已经搜索过
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  value: undefined, // 只保留结果查询，支持精确匹配
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 - 游客版本，只支持精确结果匹配 */
const getList = async () => {
  loading.value = true
  try {
    // 如果没有输入查询条件，不进行查询
    if (!queryParams.value && (!queryParams.createTime || queryParams.createTime.length === 0)) {
      list.value = []
      total.value = 0
      loading.value = false
      return
    }

    // 调用API进行精确查询
    const data = await DataInfoApi.getDataInfoPage(queryParams) // 暂时使用原API，后续可替换为游客专用API
    list.value = data.list
    total.value = data.total
    hasSearched.value = true
  } catch (error) {
    console.error('查询失败:', error)
    message.error('查询失败，请稍后重试')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  if (!queryParams.value?.trim()) {
    message.warning('请输入要查询的结果')
    return
  }
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  list.value = []
  total.value = 0
  hasSearched.value = false
}

/** 初始化 - 游客页面不自动加载数据 **/
onMounted(() => {
  // 游客页面不自动加载数据，需要用户主动搜索
})
</script>

<style scoped>
.query-page {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

/* 搜索容器样式 */
.search-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.search-header {
  padding: 24px 24px 0;
  color: white;
}

.search-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 28px;
}

.search-subtitle {
  margin: 0 0 20px 0;
  opacity: 0.9;
  font-size: 14px;
}

.search-form {
  background: rgba(255, 255, 255, 0.95);
  margin: 0 24px 24px;
  padding: 24px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.form-item-custom {
  flex: 1;
  min-width: 280px;
}

.search-input {
  width: 100%;
}

.date-picker {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.search-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.reset-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-icon {
  margin-right: 6px;
}

/* 结果容器样式 */
.result-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.result-stats {
  padding: 20px 24px;
  background: linear-gradient(90deg, #f8f9ff 0%, #e8f4fd 100%);
  border-bottom: 1px solid #e5e7eb;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  color: #3b82f6;
  font-size: 18px;
}

.stats-text {
  color: #374151;
  font-size: 14px;
}

/* 表格样式 */
.data-table {
  border-radius: 0;
}

:deep(.el-table th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8fafc !important;
}

.result-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-icon {
  color: #6366f1;
  font-size: 16px;
}

.result-text {
  font-weight: 500;
  color: #1f2937;
}

.time-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.time-icon {
  font-size: 14px;
}

/* 分页样式 */
.pagination-container {
  padding: 24px;
  display: flex;
  justify-content: center;
  background: #fafbfc;
  border-top: 1px solid #e5e7eb;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: white;
  --el-pagination-button-bg-color: white;
}

/* 空状态样式 */
.empty-state, .welcome-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #374151;
  margin: 16px 0 8px;
}

.empty-hint {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
}

.welcome-content {
  max-width: 500px;
  margin: 0 auto;
}

.welcome-title {
  font-size: 20px;
  color: #1f2937;
  margin: 16px 0 12px;
  font-weight: 600;
}

.welcome-text {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.welcome-tips {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 32px;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

.tip-icon {
  color: #3b82f6;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .query-page {
    padding: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-item-custom {
    min-width: auto;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-btn, .reset-btn {
    width: 100%;
  }

  .search-header {
    padding: 20px 20px 0;
  }

  .search-form {
    margin: 0 20px 20px;
    padding: 20px;
  }
}

/* 动画效果 */
.data-table {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
